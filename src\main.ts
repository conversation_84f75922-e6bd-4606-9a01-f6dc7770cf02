import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ConfigService } from '@nestjs/config';
import { ValidationPipe } from '@nestjs/common';
import { join } from 'path';
import { NestExpressApplication } from '@nestjs/platform-express';

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule);
  const configService = app.get(ConfigService);

  // Global validation pipe
  app.useGlobalPipes(new ValidationPipe({
    whitelist: true,
    forbidNonWhitelisted: true,
    transform: true,
  }));

  // Set global prefix for API routes
  const globalPrefix = configService.get<string>('app.globalPrefix');
  if (globalPrefix) {
    app.setGlobalPrefix(globalPrefix);
  }

  // Serve static files from the client build directory
  app.useStaticAssets(join(__dirname, '..', 'dist', 'client'), {
    prefix: '/static/',
  });

  // CORS configuration
  const corsConfig = configService.get('security.cors');
  if (corsConfig?.enabled) {
    app.enableCors({
      origin: corsConfig.origin,
      credentials: corsConfig.credentials,
    });
  }

  const port = configService.get<number>('app.port') || 3000;
  await app.listen(port);

  const appName = configService.get<string>('app.name');
  console.log(`🚀 ${appName} is running on: http://localhost:${port}`);
  console.log(`📚 API documentation available at: http://localhost:${port}/${globalPrefix}`);
}
bootstrap();
