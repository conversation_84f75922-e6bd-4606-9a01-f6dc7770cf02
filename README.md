# NexEd Nest

A modern monolithic web application built with NestJS and React, featuring Tailwind CSS, Zustand state management, TypeORM with PostgreSQL, and YAML-based configuration.

## 🚀 Features

- **Backend**: NestJS with TypeScript
- **Frontend**: React 19 with TypeScript
- **Styling**: Tailwind CSS with custom components
- **State Management**: Zustand with TypeScript support
- **Database**: TypeORM with PostgreSQL
- **Configuration**: YAML-based configuration files
- **Architecture**: Monolithic deployment with static file serving
- **Build System**: Webpack with hot reload support
- **Validation**: Class-validator for DTOs
- **Security**: CORS, validation pipes, and environment-based configs

## 📁 Project Structure

```
nexed-nest/
├── client/                 # React frontend
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── store/          # Zustand store and hooks
│   │   ├── App.tsx         # Main App component
│   │   ├── index.tsx       # React entry point
│   │   └── index.css       # Tailwind CSS imports
│   └── public/             # Static assets
├── src/                    # NestJS backend
│   ├── config/             # Configuration management
│   ├── database/           # Database module
│   ├── users/              # Users module (example)
│   ├── app.module.ts       # Main app module
│   └── main.ts             # NestJS entry point
├── config.yml              # Base configuration
├── config.development.yml  # Development config
├── config.production.yml   # Production config
├── tailwind.config.js      # Tailwind CSS config
├── postcss.config.js       # PostCSS config
└── webpack.config.js       # Webpack config
```

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

### Development

Run both frontend and backend in development mode with hot reload:

```bash
npm run start:dev
```

This will:
- Start Webpack in watch mode for React frontend
- Start NestJS in watch mode for backend
- Both will reload automatically on file changes

### Production Build

Build the entire application for production:

```bash
npm run build
```

This will:
1. Build the React frontend with Webpack (production mode)
2. Build the NestJS backend with TypeScript compiler

### Running in Production

Start the production server:

```bash
npm start
```

Or run the built application directly:

```bash
node dist/main
```

## Available Scripts

- `npm run build` - Build both client and server for production
- `npm run build:client` - Build React frontend only
- `npm run build:server` - Build NestJS backend only
- `npm start` - Build and start production server
- `npm run start:dev` - Start development mode with hot reload
- `npm run start:client:dev` - Start Webpack dev server only
- `npm run start:server:dev` - Start NestJS dev server only
- `npm run lint` - Lint backend TypeScript files
- `npm run lint:client` - Lint frontend TypeScript/React files
- `npm run test` - Run backend tests
- `npm run test:e2e` - Run end-to-end tests

## API Endpoints

- `GET /` - Serves the React application
- `GET /api/hello` - Returns JSON response from NestJS
- `GET /*` - Catch-all route for React Router (SPA support)

## Demo Features

The demo page showcases:

1. **Interactive Counter**: React state management with hooks
2. **API Integration**: Fetch data from NestJS backend
3. **Responsive Design**: CSS Grid and Flexbox layouts
4. **TypeScript**: Full type safety across frontend and backend

## Architecture Benefits

### Monolith Advantages
- **Simple Deployment**: Single build artifact
- **Shared Types**: TypeScript interfaces between frontend/backend
- **No CORS Issues**: Frontend and backend on same origin
- **Simplified Development**: Single repository and build process

### Technology Stack
- **Backend**: NestJS, Express, TypeScript
- **Frontend**: React, TypeScript, CSS3
- **Build Tools**: Webpack, Babel, TypeScript Compiler
- **Development**: Concurrently, Hot Module Replacement

## Customization

### Adding New React Components
1. Create components in `client/src/components/`
2. Import and use in `client/src/App.tsx`
3. Webpack will automatically include them in the build

### Adding New API Endpoints
1. Add routes to `src/app.controller.ts`
2. Add business logic to `src/app.service.ts`
3. NestJS will automatically register the routes

### Styling
- Global styles: `client/src/App.css`
- Component styles: Create `.css` files alongside components
- CSS Modules and Styled Components can be added if needed

## Production Considerations

- **Bundle Size**: The demo shows webpack warnings about bundle size. Consider code splitting for larger applications
- **Caching**: Add cache headers for static assets in production
- **Environment Variables**: Use different configs for development/production
- **Security**: Add helmet, CORS configuration, and other security middleware
- **Monitoring**: Add logging, metrics, and health checks

## License

This project is licensed under the UNLICENSED license.
