import React from 'react';
import DemoPage from './components/DemoPage';
import { useTheme } from './store';

const App: React.FC = () => {
  const theme = useTheme();

  return (
    <div className={`min-h-screen transition-colors duration-200 ${
      theme === 'dark' ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'
    }`}>
      <header className={`shadow-sm border-b transition-colors duration-200 ${
        theme === 'dark' ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
      }`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold">NexEd Nest</h1>
              <p className="text-sm opacity-70 mt-1">
                NestJS + React + Tailwind + Zustand + TypeORM Monolith
              </p>
            </div>
            <div className="flex items-center gap-4">
              <span className="text-sm opacity-70">
                Theme: {theme}
              </span>
            </div>
          </div>
        </div>
      </header>
      <main>
        <DemoPage />
      </main>
    </div>
  );
};

export default App;
