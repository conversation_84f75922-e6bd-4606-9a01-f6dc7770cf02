import React, { useState, useEffect } from 'react';
import { useAuth, useUI, useNotifications, useNotificationActions } from '../store';

interface ApiResponse {
  message: string;
  timestamp: string;
}

const DemoPage: React.FC = () => {
  const [count, setCount] = useState(0);
  const [apiData, setApiData] = useState<ApiResponse | null>(null);
  const [email, setEmail] = useState<string>('');
  const [password, setPassword] = useState<string>('');

  // Zustand store hooks
  const { user, isAuthenticated, login, logout } = useAuth();
  const { theme, loading, setTheme, setLoading } = useUI();
  const notifications = useNotifications();
  const { addNotification, removeNotification } = useNotificationActions();

  const fetchApiData = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/hello');
      const data = await response.json();
      setApiData(data);
      addNotification({
        type: 'success',
        title: 'API Success',
        message: 'Successfully fetched data from API'
      });
    } catch (error) {
      console.error('Error fetching API data:', error);
      setApiData({ message: 'Error fetching data', timestamp: new Date().toISOString() });
      addNotification({
        type: 'error',
        title: 'API Error',
        message: 'Failed to fetch data from API'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    if (email && password) {
      await login(email, password);
      setEmail('');
      setPassword('');
    }
  };

  const toggleTheme = () => {
    setTheme(theme === 'light' ? 'dark' : 'light');
  };

  useEffect(() => {
    fetchApiData();
  }, []);

  return (
    <div className={`min-h-screen p-6 transition-colors duration-200 ${
      theme === 'dark' ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'
    }`}>
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center">
          <h2 className="text-3xl font-bold mb-4">Demo Page</h2>
          <p className="text-lg opacity-80">
            Showcasing NestJS + React + Tailwind + Zustand integration
          </p>
        </div>

        {/* Theme Toggle */}
        <div className="card">
          <h3 className="text-xl font-semibold mb-4">Theme Control</h3>
          <div className="flex items-center gap-4">
            <span>Current theme: {theme}</span>
            <button onClick={toggleTheme} className="btn-primary">
              Switch to {theme === 'light' ? 'Dark' : 'Light'} Mode
            </button>
          </div>
        </div>

        {/* Counter Demo */}
        <div className="card">
          <h3 className="text-xl font-semibold mb-4">Interactive Counter</h3>
          <div className="flex items-center gap-4 mb-4">
            <button
              onClick={() => setCount(count - 1)}
              disabled={count <= 0}
              className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              -
            </button>
            <span className="text-2xl font-bold min-w-[3rem] text-center">{count}</span>
            <button
              onClick={() => setCount(count + 1)}
              className="btn-secondary"
            >
              +
            </button>
          </div>
          <p className="text-sm opacity-70">
            This demonstrates React state management and interactivity.
          </p>
        </div>

        {/* Authentication Demo */}
        <div className="card">
          <h3 className="text-xl font-semibold mb-4">Authentication Demo</h3>
          {isAuthenticated ? (
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                {user?.avatar && (
                  <img
                    src={user.avatar}
                    alt="Avatar"
                    className="w-12 h-12 rounded-full"
                  />
                )}
                <div>
                  <p className="font-medium">Welcome, {user?.name}!</p>
                  <p className="text-sm opacity-70">{user?.email}</p>
                </div>
              </div>
              <button onClick={logout} className="btn-secondary">
                Logout
              </button>
            </div>
          ) : (
            <form onSubmit={handleLogin} className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">Email</label>
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    theme === 'dark' ? 'bg-gray-800 border-gray-600 text-white' : 'bg-white border-gray-300'
                  }`}
                  placeholder="Enter your email"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Password</label>
                <input
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    theme === 'dark' ? 'bg-gray-800 border-gray-600 text-white' : 'bg-white border-gray-300'
                  }`}
                  placeholder="Enter your password"
                  required
                />
              </div>
              <button
                type="submit"
                disabled={loading}
                className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Logging in...' : 'Login'}
              </button>
            </form>
          )}
        </div>

        {/* API Demo */}
        <div className="card">
          <h3 className="text-xl font-semibold mb-4">API Integration</h3>
          <div className="space-y-4">
            <button
              onClick={fetchApiData}
              disabled={loading}
              className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Loading...' : 'Fetch from NestJS API'}
            </button>
            {apiData && (
              <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 className="font-medium text-blue-800 mb-2">Response from Backend:</h4>
                <p className="text-blue-700"><strong>Message:</strong> {apiData.message}</p>
                <p className="text-blue-700"><strong>Timestamp:</strong> {apiData.timestamp}</p>
              </div>
            )}
          </div>
          <p className="text-sm opacity-70 mt-4">
            This demonstrates communication between React frontend and NestJS backend.
          </p>
        </div>

        {/* Notifications Demo */}
        <div className="card">
          <h3 className="text-xl font-semibold mb-4">Notifications</h3>
          <div className="space-y-4">
            <div className="flex gap-2 flex-wrap">
              <button
                onClick={() => addNotification({
                  type: 'success',
                  title: 'Success!',
                  message: 'This is a success notification'
                })}
                className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
              >
                Add Success
              </button>
              <button
                onClick={() => addNotification({
                  type: 'error',
                  title: 'Error!',
                  message: 'This is an error notification'
                })}
                className="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
              >
                Add Error
              </button>
              <button
                onClick={() => addNotification({
                  type: 'warning',
                  title: 'Warning!',
                  message: 'This is a warning notification'
                })}
                className="px-3 py-1 bg-yellow-600 text-white rounded text-sm hover:bg-yellow-700"
              >
                Add Warning
              </button>
              <button
                onClick={() => addNotification({
                  type: 'info',
                  title: 'Info',
                  message: 'This is an info notification'
                })}
                className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
              >
                Add Info
              </button>
            </div>

            {notifications.length > 0 && (
              <div className="space-y-2">
                <h4 className="font-medium">Active Notifications:</h4>
                {notifications.slice(0, 3).map((notification) => (
                  <div
                    key={notification.id}
                    className={`p-3 rounded-lg border-l-4 ${
                      notification.type === 'success' ? 'bg-green-50 border-green-400' :
                      notification.type === 'error' ? 'bg-red-50 border-red-400' :
                      notification.type === 'warning' ? 'bg-yellow-50 border-yellow-400' :
                      'bg-blue-50 border-blue-400'
                    }`}
                  >
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="font-medium text-gray-800">{notification.title}</p>
                        <p className="text-sm text-gray-600">{notification.message}</p>
                      </div>
                      <button
                        onClick={() => removeNotification(notification.id)}
                        className="text-gray-500 hover:text-gray-700"
                      >
                        ×
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Features List */}
        <div className="card">
          <h3 className="text-xl font-semibold mb-4">Features Demonstrated</h3>
          <ul className="space-y-2">
            <li className="flex items-center gap-2">
              <span className="text-green-500">✅</span>
              <span>React components with TypeScript</span>
            </li>
            <li className="flex items-center gap-2">
              <span className="text-green-500">✅</span>
              <span>Zustand state management</span>
            </li>
            <li className="flex items-center gap-2">
              <span className="text-green-500">✅</span>
              <span>Tailwind CSS styling</span>
            </li>
            <li className="flex items-center gap-2">
              <span className="text-green-500">✅</span>
              <span>TypeORM + PostgreSQL integration</span>
            </li>
            <li className="flex items-center gap-2">
              <span className="text-green-500">✅</span>
              <span>YAML-based configuration</span>
            </li>
            <li className="flex items-center gap-2">
              <span className="text-green-500">✅</span>
              <span>API calls to NestJS backend</span>
            </li>
            <li className="flex items-center gap-2">
              <span className="text-green-500">✅</span>
              <span>Monolith deployment architecture</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default DemoPage;
