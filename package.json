{"name": "nexed-nest", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "npm run build:client && nest build", "build:client": "webpack --mode production", "build:client:dev": "webpack --mode development", "build:server": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\" \"client/src/**/*.{ts,tsx}\" \"e2e/**/*.ts\"", "start": "npm run build && node dist/main", "start:dev": "concurrently \"npm run start:client:dev\" \"npm run start:server:dev\"", "start:client:dev": "webpack --mode development --watch", "start:server:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "lint:client": "eslint \"client/src/**/*.{ts,tsx}\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/mapped-types": "^2.1.0", "@nestjs/platform-express": "^11.0.1", "@nestjs/serve-static": "^5.0.3", "@nestjs/typeorm": "^11.0.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "bcrypt": "^6.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "js-yaml": "^4.1.0", "pg": "^8.16.3", "react": "^19.1.0", "react-dom": "^19.1.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "typeorm": "^0.3.25", "zustand": "^5.0.6"}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/preset-env": "^7.28.0", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/bcrypt": "^5.0.2", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/js-yaml": "^4.0.9", "@types/node": "^22.10.7", "@types/pg": "^8.15.4", "@types/supertest": "^6.0.2", "autoprefixer": "^10.4.21", "babel-loader": "^10.0.0", "concurrently": "^9.2.0", "css-loader": "^7.1.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "file-loader": "^6.2.0", "globals": "^16.0.0", "html-webpack-plugin": "^5.6.3", "jest": "^29.7.0", "postcss": "^8.5.6", "postcss-loader": "^8.1.1", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "style-loader": "^4.0.0", "supertest": "^7.0.0", "tailwindcss": "^3.4.17", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0", "webpack": "^5.100.1", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.2"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}